package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 
 * @TableName math_question_types
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_question_types")
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class MathQuestionType extends BaseEntity {
    /**
     * 
     */
    private String name;

    /**
     * 题型描述
     */
    private String content;

    /**
     * 题型排序
     */
    private Integer sortNo;
    
    /**
     * 是否是基础题型
     */
    private Boolean isBase;

    private String category;

}