package com.joinus.knowledge.model.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PageKnowledgePointParam extends PageParam{
    private UUID id;
    private UUID textbookId;
    private Integer grade;
    private Integer semester;
    private UUID catalogNodeId;
    private String name;
    private String originalName;
    private Boolean existImage;
    private Boolean existHandout;
    private Boolean existPowerPoint;
    private Boolean existVideo;
}
