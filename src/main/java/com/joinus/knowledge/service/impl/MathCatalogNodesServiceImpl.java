package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathCatalogNodesMapper;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.service.MathCatalogNodesService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service实现
* @createDate 2025-07-30 17:33:40
*/
@Service
public class MathCatalogNodesServiceImpl extends ServiceImpl<MathCatalogNodesMapper, MathCatalogNodes>
    implements MathCatalogNodesService{

}




