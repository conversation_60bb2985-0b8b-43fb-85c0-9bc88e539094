package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.mapper.MathAnswersMapper;
import com.joinus.knowledge.mapper.MathExamsMapper;
import com.joinus.knowledge.mapper.MathQuestionsMapper;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.po.MathQuestionTypePO;
import com.joinus.knowledge.model.po.TrainingQuestionPO;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.ConverterUtils;
import com.joinus.knowledge.utils.ImageTagExtractor;
import com.joinus.knowledge.utils.MinioUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_questions】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Slf4j
@Service
public class MathQuestionsServiceImpl extends ServiceImpl<MathQuestionsMapper, MathQuestion>
    implements MathQuestionsService{

    @Autowired
    private MathAnswersMapper mathAnswersMapper;
    @Autowired
    private QuestionTypesMappingService questionTypesMappingService;
    @Autowired
    private QuestionAnswerRelationsService questionAnswerRelationsService;
    @Autowired
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Autowired
    private QuestionFileService questionFileService;
    @Autowired
    private MathQuestionTypesService mathQuestionTypesService;
    @Autowired
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Autowired
    private TextbooksService textbooksService;
    @Autowired
    private MinioUtils minioUtils;
    @Autowired
    private KnowledgePointQuestionTypeRelationshipService kpQtRelationsService;
    @Autowired
    private MathQuestionRelationshipsService mathQuestionRelationsService;
    @Autowired
    private MathLabelService mathLabelService;
    @Autowired
    private QuestionLabelService questionLabelService;
    @Autowired
    private MathQuestionDimensionService mathQuestionDimensionService;

    // 定义正则表达式，用于匹配img标签中的src属性
    private static final String IMG_SRC_REGEX = "(<img[^>]*?data-s3-key=\"[^\"]*\"[^>]*?src=\")[^\"]*(\")";
    private static Pattern imgTagPattern = Pattern.compile("<img[^>]*src=\"\"[^>]*>");
    private static Pattern dataS3EnumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
    private static Pattern dataS3KeyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");

    @Autowired
    private MathSectionService mathSectionService;
    @Autowired
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Autowired
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Autowired
    private FilesService filesService;
    @Autowired
    private MathExamsMapper mathExamsMapper;
    @Lazy
    @Autowired
    private MathExamsService mathExamsService;
    @Autowired
    @Lazy
    private MathQuestionReviewRecordsService mathQuestionReviewRecordsService;

    @Override
    public List<QuestionDetailVO> getKeypointDetail(QueryKeyPointDetailParam param) {
        List<QuestionDetailVO> results =  new ArrayList<QuestionDetailVO>();
        if (param.getType().equals("question_type")) {
            //查询题型下面的题目、答案信息
            results = baseMapper.listQuestionAndAnswerByQuestionType(param.getKeyPointId());
        } else {
            //查询考点下面的题目、答案信息
            results = baseMapper.listQuestionAndAnswerByKnowledgePoint(param.getKeyPointId());
        }
        if (CollUtil.isNotEmpty(results)) {
            results.stream().forEach(result -> {
                result.setContent(decodeContentV2(result.getContent()));
                result.getAnswers().forEach(answer -> {
                    answer.setAnswer(decodeContentV2(answer.getAnswer()));
                    answer.setContent(decodeContentV2(answer.getContent()));
                });
            });
        }
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<QuestionDetailVO> updateKeypointDetail(UpdateQuestionDetailParam param) {
        //处理content

        List<QuestionDetailParam> questionDetailList = param.getQuestionDetailList();
        questionDetailList.forEach(detailParam -> {
            OssEnum ossEnum = null != param.getOssEnum() ? param.getOssEnum() : OssEnum.MINIO_EDU_KNOWLEDGE_HUB;
            String content = encodeContent(detailParam.getContent());
            detailParam.setContent(content);

            if (detailParam.getId() == null) {
                //新增题目
                MathQuestion newQuestion = MathQuestion.builder()
                        .difficulty(detailParam.getDifficulty())
                        .questionType(detailParam.getQuestionType())
                        .content(detailParam.getContent())
                        .source(QuestionSourceType.BOOK)
                        .build();
                // 保存答案
                baseMapper.insert(newQuestion);
                detailParam.setId(newQuestion.getId());

                // 创建问题和答案的关联关系
                if (param.getType().equals("question_type")) {
                    questionTypesMappingService.createAssociation(detailParam.getId(), param.getKeyPointId());
                } else {
                    questionKnowledgePointsService.createAssociation(detailParam.getId(), param.getKeyPointId());
                }

            } else {
                MathQuestion question = baseMapper.selectById(detailParam.getId());
                if (!detailParam.getContent().equals(question.getContent()) || !detailParam.getDifficulty().equals(question.getDifficulty())
                        || !detailParam.getQuestionType().equals(question.getQuestionType())) {
                    //更新问题
                    MathQuestion updateQuestion = new MathQuestion();
                    updateQuestion.setId(detailParam.getId());
                    if (!detailParam.getContent().equals(question.getContent())) {
                        updateQuestion.setContent(detailParam.getContent());
                    }
                    if (!detailParam.getDifficulty().equals(question.getDifficulty())) {
                        updateQuestion.setDifficulty(detailParam.getDifficulty());
                    }
                    if (!detailParam.getQuestionType().equals(question.getQuestionType())) {
                        updateQuestion.setQuestionType(detailParam.getQuestionType());
                    }
                    baseMapper.updateById(updateQuestion);
                    //TODO 添加操作日志
                }
            }

            detailParam.getAnswers().forEach(answer -> {
                if (answer.getId() == null) {
                    //新增答案
                    MathAnswer newQuestionAnswer = new MathAnswer();
                    newQuestionAnswer.generateUUID(); // 生成UUID
                    newQuestionAnswer.setAnswer(encodeContent(answer.getAnswer()));
                    newQuestionAnswer.setContent(encodeContent(answer.getContent()));
                    // 保存答案
                    mathAnswersMapper.insert(newQuestionAnswer);
                    answer.setId(newQuestionAnswer.getId());


                    // 创建问题和答案的关联关系
                    questionAnswerRelationsService.createAssociation(detailParam.getId(), answer.getId());

                    // 更新传入的answer的ID，以便后续查询时可以使用
                    answer.setId(answer.getId());
                    //TODO 添加操作日志
                } else {
                    MathAnswer questionAnswer = mathAnswersMapper.selectById(answer.getId());
                    if (questionAnswer != null) {
                        // 检查是否需要更新答案
                        boolean needUpdate = false;
                        MathAnswer updateAnswer = new MathAnswer();
                        updateAnswer.setId(answer.getId());

                        if (!answer.getAnswer().equals(questionAnswer.getAnswer())) {
                            updateAnswer.setAnswer(encodeContent(answer.getAnswer()));
                            needUpdate = true;
                        }

                        if (!answer.getContent().equals(questionAnswer.getContent())) {
                            updateAnswer.setContent(encodeContent(answer.getContent()));
                            needUpdate = true;
                        }

                        if (needUpdate) {
                            // 更新答案
                            mathAnswersMapper.updateById(updateAnswer);
                        }
                    } else {
                        log.warn("Answer with ID {} not found, skipping update", answer.getId());
                    }
                }
            });

            questionFileService.saveOrUpdateBatch(detailParam.getId(), detailParam.getFiles());
        });

        QueryKeyPointDetailParam queryKeyPointDetailParam = new QueryKeyPointDetailParam();
        queryKeyPointDetailParam.setKeyPointId(param.getKeyPointId());
        queryKeyPointDetailParam.setType(param.getType());
        return getKeypointDetail(queryKeyPointDetailParam);

    }

    @Override
    public String encodeContent(String content) {
        if (content == null) {
            return "";
        }
        // 将img标签中的src属性值替换为空，保留src=""
        return content.replaceAll(IMG_SRC_REGEX, "$1$2");
    }

    public static Pattern emptyPattern = Pattern.compile("<img[^>]*?data-s3-key=\"([^\"]*)\"[^>]*?src=\"\"[^>]*?/>");

    private String decodeContent(String content, String ossType, String ossBucket) {
        if (content == null) {
            return "";
        }

        OssEnum ossEnum = OssEnum.ofTypeAndBucket(ossType, ossBucket);

        // 先用encodeContent清空src属性
        String cleanedContent = content.replaceAll(IMG_SRC_REGEX, "$1$2");

        // 然后匹配所有src为空的img标签
        Matcher matcher = emptyPattern.matcher(cleanedContent);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            // 获取data-s3-key属性值
            String s3Key = matcher.group(1);
            if (s3Key != null && !s3Key.isEmpty()) {
                // 获取文件名（从s3Key路径中提取）
                String fileName = s3Key;
                if (s3Key.contains("/")) {
                    fileName = s3Key.substring(s3Key.lastIndexOf("/") + 1);
                }

                // 获取临时下载链接（示例中使用s3Key + "temp"作为临时链接）
//                String presignedUrl = s3Key + "temp";
                String presignedUrl = "";
                if (ossEnum == OssEnum.MINIO_EDU_KNOWLEDGE_HUB) {
                    presignedUrl = minioUtils.getPresignedDownloadUrl(GlobalConstants.MINIO_BUCKET_NAME, s3Key, fileName);
                }

                // 替换空的src属性为临时链接
                String replacement = matcher.group().replace("src=\"\"", "src=\"" + presignedUrl + "\"");
                matcher.appendReplacement(sb, replacement.replace("$", "\\$").replace("\\", "\\\\"));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public String decodeContentV2(String content) {
        if (content == null) {
            return "";
        }

        Matcher imgMatcher = imgTagPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (imgMatcher.find()) {
            String tag = imgMatcher.group();

            // 提取 data-s3-enum 属性（如果存在）
            Matcher enumMatcher = dataS3EnumPattern.matcher(tag);
            String s3Enum = enumMatcher.find() ? enumMatcher.group(1) : "MINIO_EDU_KNOWLEDGE_HUB";  // 默认 aliyun

            OssEnum ossEnum = OssEnum.valueOf(s3Enum);

            // 提取 data-s3-key 属性（必须有）
            Matcher keyMatcher = dataS3KeyPattern.matcher(tag);
            if (!keyMatcher.find()) {
                continue; // 或根据需求作处理
            }
            String s3Key = keyMatcher.group(1);

            // 根据修正后的 s3Enum 和 s3Key 生成新的 src
            String newSrc = filesService.getOssUrl(s3Key, ossEnum);
            String newTag = tag.replace("src=\"\"", "src=\"" + newSrc + "\"");

            imgMatcher.appendReplacement(sb, newTag.replace("$", "\\$")); // 注意替换 $ 符号避免干扰
        }
        imgMatcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public QuestionDetailVO getDetailById(UUID uuid) {
        QuestionDetailVO detailVO = baseMapper.getDetailById(uuid);
        if (null == detailVO) {
            throw new BusinessException("题目不存在");
        }
        convertQuestionContentAndFiles(detailVO);
        List<UUID> labelIds = questionLabelService.lambdaQuery()
                .eq(QuestionLabel::getQuestionId, uuid)
                .list()
                .stream()
                .map(QuestionLabel::getLabelId)
                .distinct()
                .toList();
        if (CollUtil.isNotEmpty(labelIds)) {
            detailVO.setLabels(mathLabelService.lambdaQuery()
                    .in(MathLabel::getId, labelIds)
                    .list());
        }


        List<MathQuestionType> questionTypes = mathQuestionTypesService.listByQuestionId(uuid);
        if (questionTypes != null && !questionTypes.isEmpty()) {
            detailVO.setQuestionTypes(mathQuestionTypesService.listByIds(questionTypes.stream().map(MathQuestionType::getId).toList()));
        } else {
            detailVO.setQuestionTypes(new ArrayList<>()); // Or set to null, depending on your VO's expectation
        }

        List<MathKnowledgePoint> knowledgePoints = mathKnowledgePointsService.listByQuestionId(uuid);
        if (knowledgePoints != null && !knowledgePoints.isEmpty()) {
            detailVO.setKnowledgePoints(mathKnowledgePointsService.listByIds(knowledgePoints.stream().map(MathKnowledgePoint::getId).toList()));
        } else {
            detailVO.setKnowledgePoints(new ArrayList<>()); // Or set to null
        }
        if (detailVO.getReviewStatus() != null) {
            List<MathQuestionReviewRecords> reviewRecords = mathQuestionReviewRecordsService.lambdaQuery()
                    .eq(MathQuestionReviewRecords::getQuestionId, uuid)
                    .orderByAsc(MathQuestionReviewRecords::getCreatedAt)
                    .list();
            detailVO.setReviewRecords(reviewRecords);
        }
        return detailVO;
    }

    @Override
    public List<QuestionDetailVO> listDetailByIds(List<UUID> ids) {
        List<QuestionDetailVO> detailVOs = baseMapper.getDetailByIds(ids);

        detailVOs.forEach(detailVO -> {
           convertQuestionContentAndFiles(detailVO);
        });

        return detailVOs;
    }

    private void convertQuestionContentAndFiles(QuestionDetailVO detailVO) {
        if (StrUtil.isNotBlank(detailVO.getContent())) {
            detailVO.setContent(decodeContentV2(detailVO.getContent()));
        }

        if (CollUtil.isNotEmpty(detailVO.getFiles())) {
            detailVO.getFiles().forEach(file -> {
                file.setOssUrl(filesService.getOssUrl(file.getName(), file.getOssKey(), file.getOssType(), file.getOssBucket()));
                file.setOssEnum(OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()));
            });
            detailVO.setOriginalFiles(detailVO.getFiles().stream().filter(file -> file.getType() == 1).collect(Collectors.toList()));
            detailVO.setDerivativeFiles(detailVO.getFiles().stream().filter(file -> file.getType() == 0).collect(Collectors.toList()));
        }

        detailVO.setValidation(null == detailVO.getValidationSuccess() ? 0 : detailVO.getValidationSuccess() ? 1 : 2);
    }

    @Override
    public void deleteKeyPoints(DeleteKeypointParam param) {
//        List<MathSection> sections = mathSectionService.getByPageNo(param.getPageNo(), param.getTextbookId(), param.getSectionId());
        MathSection section = mathSectionService.getById(param.getSectionId());
        if (null == section) {
            throw new RuntimeException("小节不存在，不能删除知识点。");
        }
        if (param.getType().equals("question_type")) {
            sectionQuestionTypesService.deleteRelation(section.getId(), param.getKeypointId(), param.getPageNo());
        } else {
            sectionKnowledgePointsService.deleteAssociation(section.getId(), param.getKeypointId(), param.getPageNo());
        }
    }

    @Override
    public TextbookPointVO createKeypointByPageNo(CreateKeypointParam param) {
        if (param.getType().equals("question_type")) {
            MathQuestionType mathQuestionType = mathQuestionTypesService.save(param.getPageNo(), param.getName(), param.getTextbookId(), param.getSortNo());
            return textbooksService.getKeypointsByKeypointId(param.getType(), mathQuestionType.getId(), param.getTextbookId(), param.getPageNo());
        } else {
            boolean isExamPoint = param.getType().equals("exam_point") ? true : false;
            MathKnowledgePoint mathKnowledgePoints = mathKnowledgePointsService.save(param.getPageNo(), param.getName(), param.getTextbookId(), param.getSortNo(), isExamPoint);
            return textbooksService.getKeypointsByKeypointId(param.getType(), mathKnowledgePoints.getId(), param.getTextbookId(), param.getPageNo());
        }
    }

    @Override
    public Page<MathQuestionVO> page(Page<MathQuestionVO> pageParam, PageQuestionParam param) {

        preProcessPageQuestionParam(param);

        Page<MathQuestionVO> page = baseMapper.pageWithCustomQueryV3(pageParam, param);

        if (CollUtil.isNotEmpty(page.getRecords())) {
            postProcessPageQuestionResults(page);
        }

        return page;
    }

    private void postProcessPageQuestionResults(Page<MathQuestionVO> page) {
        List<UUID> questionIds = page.getRecords().stream().map(MathQuestionVO::getId).toList();

        List<MathLabel> labelList = mathLabelService.lambdaQuery().list();

        Map<UUID, MathLabel> labelMap = labelList.stream().collect(Collectors.toMap(MathLabel::getId, label -> label));

        Map<UUID,List<MathLabel>> questionLabelMap = questionLabelService.lambdaQuery()
                .in(QuestionLabel::getQuestionId, questionIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(QuestionLabel::getQuestionId, Collectors.mapping(QuestionLabel::getLabelId, Collectors.mapping(labelMap::get, Collectors.toList()))));


        Map<UUID, KnowledgePointAndQuestionTypeVO> kpAndQtMap = listKnowledgePointsAndQuestionTypesByIds(questionIds);

        page.getRecords().forEach(question -> {

            List<MathKnowledgePointVO> knowledgePoints = Optional.ofNullable(kpAndQtMap.getOrDefault(question.getId(), new KnowledgePointAndQuestionTypeVO()).getKnowledgePoints())
                    .orElse(List.of())
                    .stream()
                    .sorted(Comparator.comparing(MathKnowledgePointVO::getPublisher, Comparator.nullsLast(Comparator.naturalOrder())))
                    .toList();
            List<MathQuestionTypeVO> questionTypes = Optional.ofNullable(kpAndQtMap.getOrDefault(question.getId(), new KnowledgePointAndQuestionTypeVO()).getQuestionTypes())
                    .orElse(List.of())
                    .stream()
                    .sorted(Comparator.comparing(MathQuestionTypeVO::getPublisher, Comparator.nullsLast(Comparator.naturalOrder())))
                    .toList();

            MathKnowledgePointVO bookKp = knowledgePoints.stream().filter(kp -> null != kp.getPublisher()).findFirst().orElse(null);
            if (null != bookKp) {
                question.setPublisher(bookKp.getPublisher());
                question.setGrade(bookKp.getGrade());
                question.setSemester(bookKp.getSemester());
            } else {
                MathQuestionTypeVO bookQt = questionTypes.stream().filter(qt -> null != qt.getPublisher()).findFirst().orElse(null);
                if (null != bookQt) {
                    question.setPublisher(bookQt.getPublisher());
                    question.setGrade(bookQt.getGrade());
                    question.setSemester(bookQt.getSemester());
                }
            }

            question.setContent(decodeContentV2(question.getContent()));

            List<MathLabel> mathLabels = questionLabelMap.getOrDefault(question.getId(), List.of());
            question.setLabels(mathLabels.stream().map(MathLabelVO::ofLabel).toList());
            question.setKnowledgeDomains(mathLabels.stream().filter(mathLabel -> mathLabel.getType().equals("KNOWLEDGE_DOMAIN")).map(MathLabel::getName).collect(Collectors.joining(",")));
            question.setKnowledgePoints(kpAndQtMap.getOrDefault(question.getId(), new KnowledgePointAndQuestionTypeVO()).getKnowledgePoints());
            question.setQuestionTypes(kpAndQtMap.getOrDefault(question.getId(), new KnowledgePointAndQuestionTypeVO()).getQuestionTypes());
        });
    }

    private void preProcessPageQuestionParam(PageQuestionParam param) {
        if (CollUtil.isNotEmpty(param.getMathLabels())) {
            List<MathLabelParam> notContainLabels = param.getMathLabels().stream().filter(label -> label.getIds().contains(UUID.fromString("00000000-0000-0000-0000-000000000000"))).toList();

            if (CollUtil.isNotEmpty(notContainLabels)) {
                param.setNotContainLabelTypes(notContainLabels.stream().map(label -> label.getType()).toList());
            }

            List<MathLabelParam> containLabels = param.getMathLabels().stream().filter(label -> !label.getIds().contains(UUID.fromString("00000000-0000-0000-0000-000000000000"))).toList();
            if (CollUtil.isNotEmpty(containLabels)) {
                param.setContainLabels(containLabels);
            }
        }

        if (CollUtil.isNotEmpty(param.getMathQuestionDimensions())) {
            param.setContainDimensions(param.getMathQuestionDimensions().stream().filter(dimension -> !dimension.getOptions().contains(MathQuestionDimensionOptionType.NULL)).toList());
            param.setNotContainDimensions(param.getMathQuestionDimensions().stream().filter(dimension -> dimension.getOptions().contains(MathQuestionDimensionOptionType.NULL)).toList());
        }
    }

    @Override
    public List<MathAnswerVO> listAnswersByQuestionId(UUID id) {
        List<MathAnswerVO> answers = mathAnswersMapper.listAnswersByQuestionId(id);
        if (CollUtil.isEmpty(answers)) {
            return List.of();
        }
        answers.forEach(item -> {
            item.setContent(decodeContentV2(item.getContent()));
            item.setAnswer(decodeContentV2(item.getAnswer()));
            if (CollUtil.isNotEmpty(item.getFiles())) {
                item.getFiles().forEach(file -> {
                    file.setOssEnum(OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()));
                    file.setOssUrl(filesService.getOssUrl(file.getOssKey(), file.getOssEnum()));
                });

                Map<Integer, List<FileVO>> groupedFiles = item.getFiles().stream()
                        .collect(Collectors.groupingBy(FileVO::getType));

                List<FileVO> sortedAndGroupedFiles = groupedFiles.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 可选：按类型本身排序 (0, 1, 2...)
                        .flatMap(entry -> entry.getValue().stream())
                        .collect(Collectors.toList());
                item.setFiles(sortedAndGroupedFiles);

                // 如果 MathAnswerVO 有单独的字段来存储不同类型的图片，例如：
                item.setOriginalFiles(groupedFiles.getOrDefault(1, List.of()));
                item.setDerivativeFiles(groupedFiles.getOrDefault(0, List.of()));
                item.setAnalysisFiles(groupedFiles.getOrDefault(2, List.of()));
            }
        });
        return answers;
    }

    @Override
    public List<ExamQuestionPO> getQuestionIdsByKnowledgePoints(List<UUID> knowledgePoints) {
        if (CollUtil.isNotEmpty(knowledgePoints)) {
            List<UUID> questionIds = this.listQuestionIdsForTraining(knowledgePoints);

            if (CollUtil.isEmpty(questionIds)) {
                log.info("对应薄弱点未搜索到训练题目 {}", JSONUtil.toJsonStr(knowledgePoints));
                throw new BusinessException("对应薄弱点未搜索到训练题目");
            }
            return baseMapper.listQuestionByIds(questionIds);
        } else {
            return List.of();
        }
    }

    private List<UUID> listQuestionIdsForTraining(List<UUID> knowledgePoints) {
        List<UUID> questionIds = new ArrayList<UUID>();
        //根据知识点查询题目
        List<QuestionKnowledgePoint> questionKnowledgePoints = baseMapper.listTrainingQuestionIdsByKnowledgePoints(knowledgePoints, 2);
        if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
            questionIds.addAll(questionKnowledgePoints
                    .stream()
                    .map(item -> item.getQuestionId())
                    .collect(Collectors.toList()));
        }

        //根据知识点查询相关联的题型
        List<KnowledgePointQuestionTypeRelationship> kpQtRelations = kpQtRelationsService.listByKnowledgePointIds(knowledgePoints);

        if (CollUtil.isNotEmpty(kpQtRelations)) {
            //根据题型查询题目
            List<QuestionTypesMapping> questionTypeMappings = baseMapper.listQuestionIdsByQuestionTypes(kpQtRelations.stream()
                    .map(item -> item.getQuestionTypeId())
                    .collect(Collectors.toList()), 2);
            questionIds.addAll(questionTypeMappings
                    .stream()
                    .map(item -> item.getQuestionId())
                    .collect(Collectors.toList()));
        }

//            List<UUID> questionIds = CollUtil.toList(UUID.fromString("3e63715e-c2e0-4b94-b59b-af67cd48375c"),
//                    UUID.fromString("3a869b01-fc19-42c6-81a6-9a91da9e11dc"));
        return questionIds;
    }

    @Override
    public List<QuestionWithLatestAnswerVO> listNoKnowledgeDomainLabelQuestions(Integer count) {
        return baseMapper.listNoKnowledgeDomainLabelQuestions(count);
    }

    @Override
    public List<QuestionWithLatestAnswerVO> listMathQuestionByKnowledgeDomain(String labelName, Integer count) {
        return baseMapper.listMathQuestionByKnowledgeDomain(labelName, count);
    }

    @Override
    public Page<MathQuestionGraphicsScriptVO> queryQuestionGraphicsScriptByUser(Page<MathQuestionGraphicsScriptVO> pageParam, String username, String labelName, UUID questionId, String status, Date startDate, Date endDate) {
        Page<MathQuestionGraphicsScriptVO> page = baseMapper.queryQuestionGraphicsScriptByUser(pageParam, username, labelName, questionId, status, startDate, endDate);

        // 处理结果内容解码
        page.getRecords().forEach(item -> {
            item.setContent(decodeContentV2(item.getContent()));
        });

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnabled(UpdateQuestionEnabledParam param) {
        LambdaUpdateWrapper<MathQuestion> wrapper = new LambdaUpdateWrapper<MathQuestion>()
                .in(MathQuestion::getId, param.getQuestionIds())
                .set(MathQuestion::getEnabled, param.getEnabled())
                .set(MathQuestion::getUpdatedAt, new Date());
        baseMapper.update(wrapper);
    }

    @Override
    public QuestionDetailVO getParentBookQuestion(UUID id) {
        MathQuestion mathQuestion = baseMapper.selectById(id);
        if (null == mathQuestion) {
            throw new BusinessException("题目不存在");
        }
        if (QuestionSourceType.AI != mathQuestion.getSource()) {
            return null;
        }

        MathQuestionRelationships mathQuestionRelationships = mathQuestionRelationsService.getParentBookQuestion(id);
        if (null == mathQuestionRelationships) {
            return null;
        }
        return getDetailById(mathQuestionRelationships.getBaseQuestionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionDetailVO updateById(UUID id, UpdateQuestionParam param) {
        String content = param.getContent();
        if (StrUtil.isNotBlank(content)) {
            content = ConverterUtils.clearImgSrc(content);
            // 删除关系及files
            questionFileService.removeByQuestionId(id);
            // 提取content中图片列表并持久化
            List<ImageData> imageDataList = ImageTagExtractor.extractImageData(content);
            if (CollUtil.isNotEmpty(imageDataList)) {
                questionFileService.saveQuestionFileAndRelation(imageDataList.stream().map(ImageData::getDataS3key).toList(), id, imageDataList.getFirst().getDataS3Enum(), QuestionFileType.ATTACHMENT);
            }
        }

        lambdaUpdate().eq(MathQuestion::getId, id)
                .set(StrUtil.isNotBlank(content), MathQuestion::getContent, content)
                .set(null != param.getDifficulty(), MathQuestion::getDifficulty, param.getDifficulty())
                .set(null != param.getExistGraphics(), MathQuestion::getExistGraphics, param.getExistGraphics())
                .set(null != param.getEnabled(), MathQuestion::getEnabled, param.getEnabled())
                .set(null != param.getQuestionType(), MathQuestion::getQuestionType, param.getQuestionType())
                .set(MathQuestion::getUpdatedAt, new Date())
                .update();
        return getDetailById(id);
    }



    @Override
    public List<MathQuestion> listMultiKnowledgePointQuestionFromBook() {
        return baseMapper.listMultiKnowledgePointQuestionFromBook();
    }

    @Override
    public List<MathQuestion> listMultiQuestionTypesMappingFromBook() {
        return baseMapper.listMultiQuestionTypesMappingFromBook();
    }

    @Override
    public KnowledgePointAndQuestionTypeVO listKnowledgePointsAndQuestionTypes(UUID id) {
        Map<UUID, KnowledgePointAndQuestionTypeVO> questionVOMap = listKnowledgePointsAndQuestionTypesByIds(List.of(id));

        return questionVOMap.getOrDefault(id, new KnowledgePointAndQuestionTypeVO());
    }

    @Override
    public Map<UUID, KnowledgePointAndQuestionTypeVO> listKnowledgePointsAndQuestionTypesByIds(List<UUID> ids) {

        // 查询知识点信息
        List<MathKnowledgePointPO> mathKnowledgePointPOs = Optional.ofNullable(mathKnowledgePointsService.listByQuestionIds(ids)).orElse(List.of());

        // 查询题型信息
        List<MathQuestionTypePO> mathQuestionTypePOs = Optional.ofNullable(mathQuestionTypesService.listByQuestionIds(ids)).orElse(List.of());

        HashMap<UUID, KnowledgePointAndQuestionTypeVO> map = new HashMap<>();

        ids.stream().forEach(item -> {
            map.put(item, KnowledgePointAndQuestionTypeVO.builder()
                    .knowledgePoints(mathKnowledgePointPOs.stream()
                            .filter(point -> point.getQuestionId().equals(item))
                            .map(point -> BeanUtil.copyProperties(point, MathKnowledgePointVO.class))
                            .sorted(Comparator.comparing(MathKnowledgePointVO::getPublisher, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathKnowledgePointVO::getGrade, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathKnowledgePointVO::getSemester, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathKnowledgePointVO::getChapterSortNo, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathKnowledgePointVO::getSectionSortNo, Comparator.nullsLast(Comparator.naturalOrder())))
                            .toList()
                    )
                    .questionTypes(mathQuestionTypePOs.stream()
                            .filter(type -> type.getQuestionId().equals(item))
                            .map(type -> BeanUtil.copyProperties(type, MathQuestionTypeVO.class))
                            .sorted(Comparator.comparing(MathQuestionTypeVO::getPublisher, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathQuestionTypeVO::getGrade, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathQuestionTypeVO::getSemester, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathQuestionTypeVO::getChapterSortNo, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(MathQuestionTypeVO::getSectionSortNo, Comparator.nullsLast(Comparator.naturalOrder())))
                            .toList())
                    .build());
        });

        return map;
    }

    @Override
    public List<QuestionDetailVO> getDerivativeAiBookQuestion(UUID id) {
        MathQuestion mathQuestion = baseMapper.selectById(id);
        if (null == mathQuestion) {
            throw new BusinessException("题目不存在");
        }
        if (QuestionSourceType.BOOK != mathQuestion.getSource()) {
            return List.of();
        }

        List<MathQuestionRelationships> mathQuestionRelationships = mathQuestionRelationsService.getDerivativeAiBookQuestion(id);
        if (CollUtil.isEmpty(mathQuestionRelationships)) {
            return List.of();
        }
        return listDetailByIds(mathQuestionRelationships.stream().map(item -> item.getDerivedQuestionId()).toList());
    }

    @Override
    public List<QuestionDetailVO> list(String content, QuestionSourceType source) {
        source = null == source ? QuestionSourceType.BOOK : source;
        List<MathQuestion> questions = lambdaQuery().like(MathQuestion::getContent, content)
                .eq(MathQuestion::getSource, source)
                .isNull(MathQuestion::getDeletedAt)
                .list();
        if (CollUtil.isEmpty(questions)) {
            return List.of();
        }
        if (questions.size() > 20) {
            throw new BusinessException("查询到题目数量超过20条，请优化搜索内容");
        }
        List<QuestionDetailVO> results = baseMapper.listQuestionAndAnswerByIds(questions.stream().map(MathQuestion::getId).toList());
        if (CollUtil.isNotEmpty(results)) {
            results.stream().forEach(result -> {
                result.setContent(decodeContentV2(result.getContent()));
                result.getAnswers().forEach(answer -> {
                    answer.setAnswer(decodeContentV2(answer.getAnswer()));
                    answer.setContent(decodeContentV2(answer.getContent()));
                });
            });
        }
        return results;
    }

    @Override
    public void bindQuestionWithKeyPoint(UUID id, BindQuestionKeyPointParam param) {
        if ("knowledge_point".equals(param.getType()) || "exam_point".equals(param.getType())) {
            questionKnowledgePointsService.createAssociation(id, param.getKeyPointId());
        } else if ("question_type".equals(param.getType())) {
            questionTypesMappingService.createAssociation(id, param.getKeyPointId());
        } else {
            throw new BusinessException("无效的type");
        }
    }

    @Override
    public void unbindQuestionWithKeyPoint(UUID id, BindQuestionKeyPointParam param) {
        if ("knowledge_point".equals(param.getType()) || "exam_point".equals(param.getType())) {
            questionKnowledgePointsService.deleteAssociation(id, param.getKeyPointId());
        } else if ("question_type".equals(param.getType())) {
            questionTypesMappingService.deleteAssociation(id, param.getKeyPointId());
        } else {
            throw new BusinessException("无效的type");
        }
    }

    @Override
    public List<MathQuestionDimensionVO> listMathQuestionDimensions(UUID id) {
        MathQuestionDimension mathQuestionDimension = mathQuestionDimensionService.getDimensionByQuestionId(id);
        return MathQuestionDimensionVO.of(mathQuestionDimension);
    }

    @Override
    @Transactional
    public void deleteAnswer(UUID id, UUID answerId) {
        questionAnswerRelationsService.deleteAssociation(id, answerId);
        mathAnswersMapper.deleteById(answerId);
    }

    @Override
    public List<MathQuestion> listNoneKnowledgePointsExamQuestions() {
        return baseMapper.listNoneKnowledgePointsExamQuestions();
    }

    @Override
    public List<MathQuestion> listAvailableAIQuestionsByKnowledgePointId(UUID knowledgePointId) {
        List<MathQuestion> questions = baseMapper.listAvailableAIQuestionsByKnowledgePointId(knowledgePointId);
        questions.forEach(question -> question.setContent(decodeContentV2(question.getContent())));
        return questions;
    }

    @Override
    public List<MathQuestion> listAvailableAIQuestionsByQuestionTypeId(UUID questionTypeId) {
        List<MathQuestion> questions = baseMapper.listAvailableAIQuestionsByQuestionTypeId(questionTypeId);
        questions.forEach(question -> question.setContent(decodeContentV2(question.getContent())));
        return questions;
    }

    @Override
    public Map<String, Object> listMathHolidayTrainingQuestionsBySectionId(MathTrainingQuestionsParamV2 param) {
        UUID sectionId = param.getSectionId();
        if (null == sectionId) {
            throw new BusinessException("请选择小节");
        }
        List<MathKnowledgePointVO> mathKnowledgePointVOS = mathKnowledgePointsService.listBySectionIds(CollUtil.toList(sectionId));
        List<MathQuestionTypeVO> mathQuestionTypes = mathQuestionTypesService.listBySectionIds(CollUtil.toList(sectionId));

        param.setKnowledgePointIds(mathKnowledgePointVOS.stream().map(MathKnowledgePointVO::getId).toList());
        param.setQuestionTypeIds(mathQuestionTypes.stream().map(MathQuestionTypeVO::getId).toList());
        param.setQuestionCount(1);
        param.setEnablePastExamPapers(false);
        return listMathSpecialTrainingQuestionsV2(param);
    }

    @Override
    public HashMap<String, Object> listMathHolidayTrainingQuestionsByChapterId(MathTrainingQuestionsParamV2 param) {
        List<TrainingQuestionPO> trainingQuestions = baseMapper.listMathHolidayTrainingQuestions(param.getSectionId(), param.getChapterId());

        Map<String, Object> resultMap = new HashMap<>();
        if (null != param.getSectionId()) {
            resultMap = selectBalancedTrainingQuestions(trainingQuestions, 24);
        } else {
            resultMap = selectBalancedTrainingQuestionsByType(trainingQuestions);
        }

        List<TrainingQuestionPO> selectedQuestionPOs = (List<TrainingQuestionPO>)resultMap.getOrDefault("selectedQuestions", new ArrayList<>());
        if (CollUtil.isEmpty(selectedQuestionPOs)) {
            throw new BusinessException("没有找到题目");
        }

        PublisherType publisher = trainingQuestions.stream().filter(item -> item.getPublisher() != null).findFirst().get().getPublisher();
        //封装知识点题目的信息
        List<TrainingQuestionPO> kpQuestionPOs = (List<TrainingQuestionPO>) resultMap.getOrDefault("knowledgePoints", new ArrayList<>());
        Map<UUID, List<TrainingQuestionPO>> kpMap = kpQuestionPOs.stream().collect(Collectors.groupingBy(item -> item.getKnowledgePointId()));

        List<MathKnowledgePointVO> kpVOs = mathKnowledgePointsService.listByIds(kpQuestionPOs.stream().map(item -> item.getKnowledgePointId()).distinct().toList());

        if (null != publisher) {
            kpVOs = kpVOs.stream().filter(kpVO -> kpVO.getPublisher() == publisher).toList();
        }

        List<MathTrainingKnowledgePointVO> kpResults = kpVOs.stream().map(kpResult -> {
            MathTrainingKnowledgePointVO mathTrainingKnowledgePointVO = BeanUtil.copyProperties(kpResult, MathTrainingKnowledgePointVO.class);
            mathTrainingKnowledgePointVO.setKnowledgePointId(kpResult.getId());
            mathTrainingKnowledgePointVO.setKnowledgePointName(kpResult.getName());
            mathTrainingKnowledgePointVO.setQuestions(kpMap.get(kpResult.getId()).stream().map(item -> item.toMathPastExamPaperQuestionVO()).toList());
            return mathTrainingKnowledgePointVO;
        }).toList();

        //封装题型题目的信息
        List<TrainingQuestionPO> qtQuestionPOs = (List<TrainingQuestionPO>) resultMap.getOrDefault("questionTypes", new ArrayList<>());
        Map<UUID, List<TrainingQuestionPO>> qtMap = qtQuestionPOs.stream().collect(Collectors.groupingBy(item -> item.getQuestionTypeId()));

        List<MathQuestionTypeVO> qtVOs = mathQuestionTypesService.listByIds(qtQuestionPOs.stream().map(item -> item.getQuestionTypeId()).distinct().toList());

        if (null != publisher) {
            qtVOs = qtVOs.stream().filter(qtVO -> qtVO.getPublisher() == publisher).toList();
        }

        List<MathTrainingQuestionTypeVO> qtResults = qtVOs.stream().map(kpResult -> {
            MathTrainingQuestionTypeVO mathTrainingQuestionTypeVO = BeanUtil.copyProperties(kpResult, MathTrainingQuestionTypeVO.class);
            mathTrainingQuestionTypeVO.setQuestionTypeId(kpResult.getId());
            mathTrainingQuestionTypeVO.setQuestionTypeName(kpResult.getName());
            mathTrainingQuestionTypeVO.setQuestions(qtMap.get(kpResult.getId()).stream().map(item -> item.toMathPastExamPaperQuestionVO()).toList());
            return mathTrainingQuestionTypeVO;
        }).toList();


        HashMap<String, Object> result = new HashMap<>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);

        return result;
    }

    @Deprecated
    @Override
    public List<MathQuestionVO> listMathHolidayTrainingQuestionsByChapterIdV0(MathTrainingQuestionsParamV2 param) {
        List<TrainingQuestionPO> trainingQuestions = baseMapper.listMathHolidayTrainingQuestions(param.getSectionId(), param.getChapterId());

        // 调用专门的方法来选择平衡的题目
        Map<String, Object> resultMap = selectBalancedTrainingQuestionsByType(trainingQuestions);

        List<TrainingQuestionPO> selectedQuestionPOs = (List<TrainingQuestionPO>)resultMap.getOrDefault("selectedQuestions", new ArrayList<>());
        if (CollUtil.isEmpty(selectedQuestionPOs)) {
            throw new BusinessException("没有找到题目");
        }

        PublisherType publisher = trainingQuestions.stream().filter(item -> null != item.getPublisher()).findFirst().get().getPublisher();
        //封装知识点题目的信息
        List<MathQuestionVO> results = new ArrayList<>();
        List<TrainingQuestionPO> kpQuestionPOs = (List<TrainingQuestionPO>) resultMap.getOrDefault("knowledgePoints", new ArrayList<>());
        kpQuestionPOs = kpQuestionPOs.stream().sorted(Comparator.comparing(TrainingQuestionPO::getPageIndex, Comparator.nullsLast(Comparator.naturalOrder()))).toList();
        List<MathKnowledgePointPO> kps = mathKnowledgePointsService.listByQuestionIdsAndPublisher(kpQuestionPOs.stream().map(po -> po.getQuestionId()).toList(), publisher);
        Map<UUID, List<MathKnowledgePointPO>> kpCollect = kps.stream().collect(Collectors.groupingBy(kp -> kp.getQuestionId()));

        //封装题型题目的信息
        List<TrainingQuestionPO> qtQuestionPOs = (List<TrainingQuestionPO>) resultMap.getOrDefault("questionTypes", new ArrayList<>());
        qtQuestionPOs = qtQuestionPOs.stream().sorted(Comparator.comparing(TrainingQuestionPO::getPageIndex, Comparator.nullsLast(Comparator.naturalOrder()))).toList();
        List<MathQuestionTypePO> qts = mathQuestionTypesService.listByQuestionIdsAndPublisher(qtQuestionPOs.stream().map(po -> po.getQuestionId()).toList(), publisher);
        Map<UUID, List<MathQuestionTypePO>> qtCollect = qts.stream().collect(Collectors.groupingBy(qt -> qt.getQuestionId()));

        results = selectedQuestionPOs.stream().map(po -> {
            MathQuestionVO questionVO = MathQuestionVO.builder()
                    .id(po.getQuestionId())
                    .questionType(po.getQuestionType())
                    .build();
            if (po.getKnowledgePointId() != null) {
                questionVO.setKnowledgePoints(kpCollect.get(po.getQuestionId()).stream().map(kp -> kp.convertToVO()).toList());
            }
            if (po.getQuestionTypeId() != null) {
                questionVO.setQuestionTypes(qtCollect.get(po.getQuestionId()).stream().map(kp -> kp.convertToVO()).toList());
            }
            return questionVO;
        }).toList();

        return results;
    }

    @Override
    public List<MathQuestionVO> listMathHolidayTrainingQuestions(MathTrainingQuestionsParamV2 param) {
        MathExamPageQueryParam examParam = MathExamPageQueryParam.builder()
                .grade(param.getGrade())
                .semester(param.getSemester())
                .publisher(param.getPublisher())
                .source(ExamSourceType.REGULAR_EXAM_PAPER)
                .build();
        IPage<MathExamVO> exams = mathExamsService.pageQuery(new Page<>(1, 100), examParam);
        if (CollUtil.isEmpty(exams.getRecords())) {
            throw new BusinessException("没有找到相关试卷");
        }
        MathExamVO mathExamVO = exams.getRecords().get(0);

        List<ExamQuestionPO> examQuestionPOS = mathExamsService.listQuestionByExamId(mathExamVO.getId());

        List<MathQuestionVO> results = new ArrayList<>();
        examQuestionPOS.stream().sorted(Comparator.comparing((ExamQuestionPO po)  -> po.getQuestionType().getSortNo())
                        .thenComparing(ExamQuestionPO::getSortNo, Comparator.nullsLast(Comparator.naturalOrder())))
                .forEach(po -> {
                    MathQuestionVO questionVO = MathQuestionVO.builder()
                            .id(po.getQuestionId())
                            .sortNo(po.getSortNo())
                            .questionType(po.getQuestionType())
                            .build();
                    results.add(questionVO);
                });

        //装配知识点信息
        List<MathKnowledgePointPO> kps = mathKnowledgePointsService.listByQuestionIdsAndPublisher(results.stream().map(po -> po.getId()).toList(), param.getPublisher());
        Map<UUID, List<MathKnowledgePointPO>> questionKpMap = kps.stream().collect(Collectors.groupingBy(kp -> kp.getQuestionId()));
        results.stream().forEach(vo -> {
            List<MathKnowledgePointPO> kpPOs = questionKpMap.getOrDefault(vo.getId(), new ArrayList<>());
            if (CollUtil.isNotEmpty(kpPOs)) {
                vo.setKnowledgePoints(kpPOs.stream().map(kp -> kp.convertToVO()).toList());
            }
        });
        return results;
    }

    @Override
    public List<MathQuestionVO> listQuestionsByExamIdForTraining(UUID examId) {

        MathExam exam = mathExamsService.getById(examId);

        List<ExamQuestionPO> examQuestionPOS = mathExamsService.listQuestionByExamId(examId);
        if (CollUtil.isEmpty(examQuestionPOS)) {
            throw new BusinessException("没有找到相关题目");
        }

        List<MathQuestionVO> results = new ArrayList<>();
        examQuestionPOS.stream().sorted(Comparator.comparing((ExamQuestionPO po) -> po.getQuestionType().getSortNo())
                        .thenComparing(ExamQuestionPO::getSortNo, Comparator.nullsLast(Comparator.naturalOrder())))
                .forEach(po -> {
                    MathQuestionVO questionVO = MathQuestionVO.builder()
                            .id(po.getQuestionId())
                            .sortNo(po.getSortNo())
                            .questionType(po.getQuestionType())
                            .build();
                    results.add(questionVO);
                });

        //装配知识点信息
        List<MathKnowledgePointPO> kps = mathKnowledgePointsService.listByQuestionIdsAndPublisher(results.stream().map(po -> po.getId()).toList(), exam.getPublisher());
        Map<UUID, List<MathKnowledgePointPO>> questionKpMap = kps.stream().collect(Collectors.groupingBy(kp -> kp.getQuestionId()));
        results.stream().forEach(vo -> {
            List<MathKnowledgePointPO> kpPOs = questionKpMap.getOrDefault(vo.getId(), new ArrayList<>());
            if (CollUtil.isNotEmpty(kpPOs)) {
                vo.setKnowledgePoints(kpPOs.stream().map(kp -> kp.convertToVO()).toList());
            }
        });
        return results;

    }

    @Override
    public Boolean checkSolved(MathQuestion question) {
        List<MathAnswerVO> mathAnswerVOS = listAnswersByQuestionId(question.getId());
        return CollUtil.isNotEmpty(mathAnswerVOS);
    }

    @Override
    public Boolean checkRelatedKnowledgePoints(MathQuestion question, PublisherType publisher) {
        List<MathKnowledgePointPO> mathKnowledgePointPOS = mathKnowledgePointsService.listByQuestionIdsAndPublisher(CollUtil.toList(question.getId()), publisher);
        return CollUtil.isNotEmpty(mathKnowledgePointPOS);
    }

    @Override
    public Map<String, Object> listQuestionsByExamIdForTraining(UUID examId, ExamSourceType source) {

        MathExam exam = mathExamsService.getById(examId);
        List<ExamQuestionPO> examQuestionPOS = mathExamsService.listQuestionByExamId(examId);
        if (CollUtil.isEmpty(examQuestionPOS)) {
            throw new BusinessException("没有找到相关题目");
        }
        Map<UUID, Integer> questionSortNoMap = examQuestionPOS.stream().collect(Collectors.toMap(ExamQuestionPO::getQuestionId, ExamQuestionPO::getSortNo));

        log.info("listQuestionsByExamIdForTraining exam {} pos {}", JSONUtil.toJsonStr(exam), JSONUtil.toJsonStr(examQuestionPOS));

        log.info("listQuestionsByExamIdForTraining questionIds {}", examQuestionPOS.stream().map(po -> po.getQuestionId()).toList());
        List<MathKnowledgePointPO> kpPOs = mathKnowledgePointsService.listByQuestionIdsAndPublisher(examQuestionPOS.stream().map(po -> po.getQuestionId()).toList(), exam.getPublisher());

        log.info("listQuestionsByExamIdForTraining kpPOs {}", JSONUtil.toJsonStr(kpPOs));

        Map<UUID, List<MathKnowledgePointPO>> kpQuestionMap = kpPOs.stream().collect(Collectors.groupingBy(kp -> kp.getId()));
        log.info("listQuestionsByExamIdForTraining kpQuestionMap {}", JSONUtil.toJsonStr(kpQuestionMap));

        List<MathTrainingKnowledgePointVO> kpResults = kpPOs.stream().map(kpPO -> {
            MathTrainingKnowledgePointVO mathTrainingKnowledgePointVO = BeanUtil.copyProperties(kpPO, MathTrainingKnowledgePointVO.class);
            mathTrainingKnowledgePointVO.setKnowledgePointId(kpPO.getId());
            mathTrainingKnowledgePointVO.setKnowledgePointName(kpPO.getName());
            mathTrainingKnowledgePointVO.setQuestions(kpQuestionMap.get(kpPO.getId()).stream().map(item -> item.toMathPastExamPaperQuestionVO()).toList());
            return mathTrainingKnowledgePointVO;
        }).toList();
        log.info("listQuestionsByExamIdForTraining kpResults {}", JSONUtil.toJsonStr(kpResults));
        if (CollUtil.isNotEmpty(kpResults)) {
            kpResults.stream().forEach(kp -> {
                kp.getQuestions().stream().forEach(question -> {question.setSortNo(questionSortNoMap.get(question.getId()));});
            });
        }


        List<MathQuestionTypePO> qtPOs = mathQuestionTypesService.listByQuestionIdsAndPublisher(examQuestionPOS.stream().map(po -> po.getQuestionId()).toList(), exam.getPublisher());
        log.info("listQuestionsByExamIdForTraining qtPOs {}", JSONUtil.toJsonStr(qtPOs));

        Map<UUID, List<MathQuestionTypePO>> qtQuestionMap = qtPOs.stream().collect(Collectors.groupingBy(qt -> qt.getId()));

        log.info("listQuestionsByExamIdForTraining qtQuestionMap {}", JSONUtil.toJsonStr(qtQuestionMap));

        List<MathTrainingQuestionTypeVO> qtResults = qtPOs.stream().map(qtPO -> {
            MathTrainingQuestionTypeVO mathTrainingQuestionTypeVO = BeanUtil.copyProperties(qtPO, MathTrainingQuestionTypeVO.class);
            mathTrainingQuestionTypeVO.setQuestionTypeId(qtPO.getId());
            mathTrainingQuestionTypeVO.setQuestionTypeName(qtPO.getName());
            mathTrainingQuestionTypeVO.setQuestions(qtQuestionMap.get(qtPO.getId()).stream().map(item -> item.toMathPastExamPaperQuestionVO()).toList());
            return mathTrainingQuestionTypeVO;
        }).toList();

        log.info("listQuestionsByExamIdForTraining qtResults {}", JSONUtil.toJsonStr(qtResults));
        if (CollUtil.isNotEmpty(qtResults)) {
            qtResults.stream().forEach(qt -> {
                qt.getQuestions().stream().forEach(question -> {question.setSortNo(questionSortNoMap.get(question.getId()));});
            });
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);
        return result;
    }

    /**
     * 从训练题目中随机选择指定数量的题目，并尽可能平均地分配在不同的知识点和题型中
     *
     * @param trainingQuestions 所有可选的训练题目
     * @param totalQuestions 需要选择的题目总数
     * @return 包含选择结果的Map，包括按知识点和题型分类的结果
     */
    private Map<String, Object> selectBalancedTrainingQuestions(List<TrainingQuestionPO> trainingQuestions, int totalQuestions) {
        // 按知识点和题型分组，并按reviewStatus排序
        Map<UUID, List<TrainingQuestionPO>> knowledgePointMap = new HashMap<>();
        Map<UUID, List<TrainingQuestionPO>> questionTypeMap = new HashMap<>();

        // 对有知识点ID的题目进行分组
        trainingQuestions.stream()
                .filter(q -> q.getKnowledgePointId() != null)
                .forEach(q -> {
                    knowledgePointMap.computeIfAbsent(q.getKnowledgePointId(), k -> new ArrayList<>()).add(q);
                });

        // 对有题型ID的题目进行分组
        trainingQuestions.stream()
                .filter(q -> q.getQuestionTypeId() != null)
                .forEach(q -> {
                    questionTypeMap.computeIfAbsent(q.getQuestionTypeId(), k -> new ArrayList<>()).add(q);
                });

        // 计算每个知识点和题型应该选择的题目数量
        int knowledgePointCount = knowledgePointMap.size();
        int questionTypeCount = questionTypeMap.size();

        // 如果没有知识点或题型，直接返回空结果
        if (knowledgePointCount == 0 && questionTypeCount == 0) {
            return new HashMap<>();
        }

        // 计算知识点和题型应该分配的题目数量比例
        // 默认各占一半，如果其中一类为空，则另一类占全部
        int kpQuestionCount = questionTypeCount == 0 ? totalQuestions : totalQuestions / 2;
        int qtQuestionCount = knowledgePointCount == 0 ? totalQuestions : totalQuestions - kpQuestionCount;

        // 存储最终选择的题目
        Set<UUID> selectedQuestionIds = new HashSet<>();
        List<TrainingQuestionPO> selectedQuestions = new ArrayList<>();

        // 1. 从知识点中选择题目
        if (knowledgePointCount > 0) {
            // 计算每个知识点应选择的题目数量（至少为1）
            int questionsPerKp = Math.max(1, kpQuestionCount / knowledgePointCount);

            // 随机打乱知识点顺序
            List<UUID> kpIds = new ArrayList<>(knowledgePointMap.keySet());
            Collections.shuffle(kpIds);

            // 从每个知识点中随机选择题目
            for (UUID kpId : kpIds) {
                List<TrainingQuestionPO> questions = knowledgePointMap.get(kpId);
                // 计算当前知识点应选择的题目数量
                int toSelect = Math.min(questionsPerKp, questions.size());
                // 确保不超过总体需要的题目数量
                toSelect = Math.min(toSelect, kpQuestionCount - selectedQuestionIds.size());

                // 按照审核状态优先级选择题目
                selectQuestionsByReviewStatus(questions, toSelect, selectedQuestionIds, selectedQuestions);

                // 如果已经选够了知识点题目，就退出循环
                if (selectedQuestionIds.size() >= kpQuestionCount) {
                    break;
                }
            }

            // 如果还没选够，继续从剩余知识点中选择
            if (selectedQuestionIds.size() < kpQuestionCount) {
                List<TrainingQuestionPO> remainingKpQuestions = trainingQuestions.stream()
                        .filter(q -> q.getKnowledgePointId() != null && !selectedQuestionIds.contains(q.getQuestionId()))
                        .collect(Collectors.toList());
                // 按照审核状态优先级选择剩余题目
                int remainingToSelect = kpQuestionCount - selectedQuestionIds.size();
                selectQuestionsByReviewStatus(remainingKpQuestions, remainingToSelect, selectedQuestionIds, selectedQuestions);
            }
        }

        // 2. 从题型中选择题目
        if (questionTypeCount > 0) {
            // 计算每个题型应选择的题目数量（至少为1）
            int questionsPerQt = Math.max(1, qtQuestionCount / questionTypeCount);

            // 随机打乱题型顺序
            List<UUID> qtIds = new ArrayList<>(questionTypeMap.keySet());
            Collections.shuffle(qtIds);

            // 从每个题型中随机选择题目
            for (UUID qtId : qtIds) {
                List<TrainingQuestionPO> questions = questionTypeMap.get(qtId);
                // 计算当前题型应选择的题目数量
                int toSelect = Math.min(questionsPerQt, questions.size());
                // 确保不超过总体需要的题目数量，并考虑已经选择的题目
                toSelect = Math.min(toSelect, totalQuestions - selectedQuestionIds.size());

                // 按照审核状态优先级选择题目
                selectQuestionsByReviewStatus(questions, toSelect, selectedQuestionIds, selectedQuestions);

                // 如果已经选够了总题目数量，就退出循环
                if (selectedQuestionIds.size() >= totalQuestions) {
                    break;
                }
            }

            // 如果还没选够，继续从剩余题型中选择
            if (selectedQuestionIds.size() < totalQuestions) {
                List<TrainingQuestionPO> remainingQtQuestions = trainingQuestions.stream()
                        .filter(q -> q.getQuestionTypeId() != null && !selectedQuestionIds.contains(q.getQuestionId()))
                        .collect(Collectors.toList());

                // 按照审核状态优先级选择剩余题目
                int remainingToSelect = qtQuestionCount - (selectedQuestionIds.size() - kpQuestionCount);
                selectQuestionsByReviewStatus(remainingQtQuestions, remainingToSelect, selectedQuestionIds, selectedQuestions);
            }
        }

        // 3. 如果还没选够，从所有题目中随机选择
        if (selectedQuestionIds.size() < totalQuestions) {
            List<TrainingQuestionPO> remainingQuestions = trainingQuestions.stream()
                    .filter(q -> !selectedQuestionIds.contains(q.getQuestionId()))
                    .collect(Collectors.toList());

            Collections.shuffle(remainingQuestions);

            for (TrainingQuestionPO question : remainingQuestions) {
                if (selectedQuestionIds.add(question.getQuestionId())) {
                    selectedQuestions.add(question);
                }

                if (selectedQuestionIds.size() >= totalQuestions) {
                    break;
                }
            }
        }

        // 按知识点和题型分类结果
        List<TrainingQuestionPO> kpResults = selectedQuestions.stream()
                .filter(po -> null != po.getKnowledgePointId())
                .collect(Collectors.toList());

        List<TrainingQuestionPO> qtResults = selectedQuestions.stream()
                .filter(po -> null != po.getQuestionTypeId())
                .collect(Collectors.toList());

        // 返回结果
        HashMap<String, Object> result = new HashMap<>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);
        result.put("selectedQuestions", selectedQuestions);
        result.put("totalSelected", selectedQuestionIds.size());
        return result;
    }

    @Override
    public List<QuestionDetailVO> listExamQuestionDetailByExamId(UUID examId) {
        return baseMapper.listExamQuestionDetailByExamId(examId);
    }

    @Override
    public Map<String, Object> listMathSpecialTrainingQuestionsV2(MathTrainingQuestionsParamV2 param) {
        HashMap<String, Object> result = new HashMap<>();
        Integer questionCount = param.getQuestionCount();
        Integer grade = null;
        Integer semester = null;
        PublisherType publisher = null;
        if (null != param.getExamId()) {
            MathExam mathExam = mathExamsMapper.selectById(param.getExamId());
//            grade = mathExam.getGrade();
//            semester = mathExam.getSemester();
//            publisher = mathExam.getPublisher();
        }

        List<UUID> knowledgePointIds = param.getKnowledgePointIds();
        List<UUID> questionTypeIds = param.getQuestionTypeIds();

        List<MathTrainingKnowledgePointVO> kpResults = new ArrayList<>();
        if (CollUtil.isNotEmpty(knowledgePointIds)) {
            //查询知识点关联的ai题目信息
            List<QuestionKnowledgePoint> questionKnowledgePoints = baseMapper.listTrainingQuestionIdsByKnowledgePoints(knowledgePointIds, questionCount);
            Map<UUID, List<MathPastExamPaperQuestionVO>> kpQuestionMap = questionKnowledgePoints.stream()
                    .collect(Collectors.groupingBy(
                            QuestionKnowledgePoint::getKnowledgePointId,
                            Collectors.mapping(
                                    qkp -> MathPastExamPaperQuestionVO.builder().id(qkp.getQuestionId()).isPastExamPaper(false).build(),
                                    Collectors.toList()
                            )
                    ));
            //查询知识点信息
            List<MathKnowledgePointVO> mathKnowledgePoints = mathKnowledgePointsService.listByIdsAndPublisher(knowledgePointIds, publisher, grade, semester);
            Map<UUID, MathKnowledgePointVO> kpMap = mathKnowledgePoints.stream()
                    .collect(Collectors.toMap(
                            MathKnowledgePointVO::getId,
                            Function.identity(),
                            (existing, replacement) -> existing // 重复时保留第一个
                    ));

            //查询知识点关联的历年真题信息
            final Map<UUID, List<MathPastExamPaperQuestionVO>> qkpPastExamPagerMap;
            if (param.getEnablePastExamPapers()) {
                List<QuestionKnowledgePointDTO> pastExamPaperQuestions = baseMapper.listPastExamPaperQuestions(knowledgePointIds, questionCount, QuestionSourceType.ZHONG_KAO_EXAM);
                qkpPastExamPagerMap = pastExamPaperQuestions.stream()
                        .collect(Collectors.groupingBy(
                                QuestionKnowledgePointDTO::getKnowledgePointId,
                                Collectors.mapping(
                                        qkp -> MathPastExamPaperQuestionVO.builder()
                                                .id(qkp.getQuestionId())
                                                .isPastExamPaper(true)
                                                .pastExamPaperYear(qkp.getPastExamPaperYear())
                                                .pastExamPaperRegion(qkp.getPastExamPaperRegion())
                                                .build(), Collectors.toList()
                                )
                        ));
            } else {
                qkpPastExamPagerMap = new HashMap<>();
            }

            //组装知识点信息和题目id
            kpResults = knowledgePointIds.stream().map(kpId -> {
                MathKnowledgePointVO mathQuestionTypeVO = kpMap.get(kpId);
                if (null == mathQuestionTypeVO) return null;

                // 获取普通题目
                List<MathPastExamPaperQuestionVO> normalQuestions = kpQuestionMap.getOrDefault(kpId, List.of());
                // 获取历年真题
                List<MathPastExamPaperQuestionVO> pastExamQuestions = qkpPastExamPagerMap.getOrDefault(kpId, List.of());

                // 合并两种题目
                List<MathPastExamPaperQuestionVO> allQuestions = new ArrayList<>();
                allQuestions.addAll(normalQuestions);
                allQuestions.addAll(pastExamQuestions);

                MathTrainingKnowledgePointVO kpVO = MathTrainingKnowledgePointVO.builder()
                        .knowledgePointId(kpMap.get(kpId).getId())
                        .knowledgePointName(kpMap.get(kpId).getName())
                        .questions(allQuestions)
                        .build();
                BeanUtil.copyProperties(kpMap.get(kpId), kpVO);
                return kpVO;
            }).filter(Objects::nonNull)
            .toList();
        }

        List<MathTrainingQuestionTypeVO> qtResults = new ArrayList<>();
        if (CollUtil.isNotEmpty(questionTypeIds)) {
            //查询题型对应的ai题目信息
            List<QuestionTypesMapping> questionTypesMappings = baseMapper.listQuestionIdsByQuestionTypes(questionTypeIds, questionCount);
            Map<UUID, List<MathPastExamPaperQuestionVO>> qtQuestionMap = questionTypesMappings.stream()
                    .collect(Collectors.groupingBy(
                            QuestionTypesMapping::getQuestionTypeId,
                            Collectors.mapping(
                                    qtm -> MathPastExamPaperQuestionVO.builder().id(qtm.getQuestionId()).isPastExamPaper(false).build(),
                                    Collectors.toList()
                            )
                    ));
            //查询题型信息
            List<MathQuestionTypeVO> mathQuestionTypes = mathQuestionTypesService.listByIdsAndPublisher(questionTypeIds, publisher, grade, semester);
            Map<UUID, MathQuestionTypeVO> qtMap = mathQuestionTypes.stream()
                    .collect(Collectors.toMap(
                            MathQuestionTypeVO::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            //组装题型信息和题目id
            qtResults = questionTypeIds.stream().map(qtId -> {
                MathQuestionTypeVO mathQuestionTypeVO = qtMap.get(qtId);
                if (null == mathQuestionTypeVO) return null;
                MathTrainingQuestionTypeVO qtVO = MathTrainingQuestionTypeVO.builder()
                        .questionTypeId(qtMap.get(qtId).getId())
                        .questionTypeName(qtMap.get(qtId).getName())
                        .questions(qtQuestionMap.getOrDefault(qtId, List.of()))
                        .build();
                BeanUtil.copyProperties(qtMap.get(qtId), qtVO);
                return qtVO;
            }).filter(Objects::nonNull)
            .toList();

        }

        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);

        return result;
    }

    /**
     * 从题目列表中按审核状态优先级选择指定数量的题目
     * @param questions 题目列表
     * @param maxToSelect 最多选择的题目数量
     * @param selectedQuestionIds 已选择的题目ID集合
     * @param selectedQuestions 已选择的题目列表
     * @return 实际选择的题目数量
     */
    private int selectQuestionsByReviewStatus(List<TrainingQuestionPO> questions, int maxToSelect,
                                           Set<UUID> selectedQuestionIds, List<TrainingQuestionPO> selectedQuestions) {
        if (questions == null || questions.isEmpty() || maxToSelect <= 0) {
            return 0;
        }

        int selected = 0;

        // 1. 优先选择APPROVED_SECOND_REVIEW的题目
        List<TrainingQuestionPO> secondReview = questions.stream()
                .filter(q -> q.getReviewStatus() != null &&
                           q.getReviewStatus().name().equals("APPROVED_SECOND_REVIEW") &&
                           !selectedQuestionIds.contains(q.getQuestionId()))
                .collect(Collectors.toList());
        Collections.shuffle(secondReview);

        for (TrainingQuestionPO question : secondReview) {
            if (selected >= maxToSelect) break;
            if (selectedQuestionIds.add(question.getQuestionId())) {
                selectedQuestions.add(question);
                selected++;
            }
        }

        // 2. 如果还需要更多题目，选择APPROVED_FIRST_REVIEW的题目
        if (selected < maxToSelect) {
            List<TrainingQuestionPO> firstReview = questions.stream()
                    .filter(q -> q.getReviewStatus() != null &&
                               q.getReviewStatus().name().equals("APPROVED_FIRST_REVIEW") &&
                               !selectedQuestionIds.contains(q.getQuestionId()))
                    .collect(Collectors.toList());
            Collections.shuffle(firstReview);

            for (TrainingQuestionPO question : firstReview) {
                if (selected >= maxToSelect) break;
                if (selectedQuestionIds.add(question.getQuestionId())) {
                    selectedQuestions.add(question);
                    selected++;
                }
            }
        }

        // 3. 如果还需要更多题目，从剩余题目中选择
        if (selected < maxToSelect) {
            List<TrainingQuestionPO> otherQuestions = questions.stream()
                    .filter(q -> !selectedQuestionIds.contains(q.getQuestionId()) &&
                               (q.getReviewStatus() == null ||
                                (!q.getReviewStatus().name().equals("APPROVED_SECOND_REVIEW") &&
                                 !q.getReviewStatus().name().equals("APPROVED_FIRST_REVIEW"))))
                    .collect(Collectors.toList());
            Collections.shuffle(otherQuestions);

            for (TrainingQuestionPO question : otherQuestions) {
                if (selected >= maxToSelect) break;
                if (selectedQuestionIds.add(question.getQuestionId())) {
                    selectedQuestions.add(question);
                    selected++;
                }
            }
        }

        return selected;
    }

    /**
     * 从训练题目中按照题目类型优先级选择题目，并尽可能平均地分配在不同的知识点和题型中
     * 优先规则：10道选择题、10道填空题、4道解答题 > 知识点和题型平均分配 > 总题目数量24道
     * 如果某类型题目不足，则保持空缺而不用其他类型填充
     * 题目选择时按照审核状态优先级：APPROVED_SECOND_REVIEW > APPROVED_FIRST_REVIEW > 其他
     *
     * @param trainingQuestions 所有可选的训练题目
     * @return 包含选择结果的Map，包括按知识点和题型分类的结果
     */
    private Map<String, Object> selectBalancedTrainingQuestionsByType(List<TrainingQuestionPO> trainingQuestions) {
        // 按题目类型分组（选择题、填空题、解答题）
        Map<QuestionType, List<TrainingQuestionPO>> questionsByType = new HashMap<>();
        // 按知识点分组
        Map<UUID, List<TrainingQuestionPO>> knowledgePointMap = new HashMap<>();
        // 按题型分组
        Map<UUID, List<TrainingQuestionPO>> questionTypeMap = new HashMap<>();

        // 对题目进行分类
        for (TrainingQuestionPO question : trainingQuestions) {
            // 按题目类型分类（选择、填空、解答）
            QuestionType questionType = question.getQuestionType();
            if (questionType != null) {
                questionsByType.computeIfAbsent(questionType, k -> new ArrayList<>()).add(question);
            }

            // 按知识点分类
            if (question.getKnowledgePointId() != null) {
                knowledgePointMap.computeIfAbsent(question.getKnowledgePointId(), k -> new ArrayList<>()).add(question);
            }

            // 按题型分类
            if (question.getQuestionTypeId() != null) {
                questionTypeMap.computeIfAbsent(question.getQuestionTypeId(), k -> new ArrayList<>()).add(question);
            }
        }

        // 存储最终选择的题目
        Set<UUID> selectedQuestionIds = new HashSet<>();
        List<TrainingQuestionPO> selectedQuestions = new ArrayList<>();

        // 目标题目数量
        final int TARGET_CHOICE_COUNT = 10;    // 选择题目标数量
        final int TARGET_FILL_COUNT = 10;      // 填空题目标数量
        final int TARGET_SOLUTION_COUNT = 4;   // 解答题目标数量

        // 1. 首先按题目类型选择题目（选择题、填空题、解答题）
        Map<QuestionType, Integer> typeTargets = new HashMap<>();
        typeTargets.put(QuestionType.MULTIPLE_CHOICE, TARGET_CHOICE_COUNT);
        typeTargets.put(QuestionType.FILL_IN_THE_BLANK, TARGET_FILL_COUNT);
        typeTargets.put(QuestionType.PROBLEM_SOLVING, TARGET_SOLUTION_COUNT);

        // 对每种题目类型，尽可能平均地从不同知识点和题型中选择
        for (Map.Entry<QuestionType, Integer> typeTarget : typeTargets.entrySet()) {
            QuestionType type = typeTarget.getKey();
            int targetCount = typeTarget.getValue();
            List<TrainingQuestionPO> questionsOfType = questionsByType.getOrDefault(type, new ArrayList<>());

            // 如果该类型题目不足，则只选择可用的题目数量
            int availableCount = questionsOfType.size();
            if (availableCount == 0) {
                continue; // 如果没有该类型题目，则跳过
            }

            // 按知识点分组该类型的题目
            Map<UUID, List<TrainingQuestionPO>> kpQuestionsOfType = new HashMap<>();
            for (TrainingQuestionPO q : questionsOfType) {
                if (q.getKnowledgePointId() != null) {
                    kpQuestionsOfType.computeIfAbsent(q.getKnowledgePointId(), k -> new ArrayList<>()).add(q);
                }
            }

            // 按题型分组该类型的题目
            Map<UUID, List<TrainingQuestionPO>> qtQuestionsOfType = new HashMap<>();
            for (TrainingQuestionPO q : questionsOfType) {
                if (q.getQuestionTypeId() != null) {
                    qtQuestionsOfType.computeIfAbsent(q.getQuestionTypeId(), k -> new ArrayList<>()).add(q);
                }
            }

            // 计算每个知识点和题型应选择的题目数量
            int kpCount = kpQuestionsOfType.size();
            int qtCount = qtQuestionsOfType.size();

            // 计算知识点和题型应该分配的题目数量比例
            // 默认各占一半，如果其中一类为空，则另一类占全部
            int kpQuestionCount = qtCount == 0 ? targetCount : targetCount / 2;
            int qtQuestionCount = kpCount == 0 ? targetCount : targetCount - kpQuestionCount;

            // 从知识点中选择题目
            if (kpCount > 0) {
                // 计算每个知识点应选择的题目数量（至少为1）
                int questionsPerKp = Math.max(1, kpQuestionCount / kpCount);

                // 按审核状态对知识点进行排序（二审通过 > 一审通过 > 未通过）
                List<Map.Entry<UUID, List<TrainingQuestionPO>>> kpEntries = new ArrayList<>(kpQuestionsOfType.entrySet());
                kpEntries.sort((e1, e2) -> {
                    // 获取每个知识点的最高审核状态
                    int status1 = e1.getValue().stream()
                            .mapToInt(q -> q.getReviewStatus() != null ? q.getReviewStatus().getPriority() : 0)
                            .max().orElse(0);
                    int status2 = e2.getValue().stream()
                            .mapToInt(q -> q.getReviewStatus() != null ? q.getReviewStatus().getPriority() : 0)
                            .max().orElse(0);
                    // 降序排序
                    return Integer.compare(status2, status1);
                });

                // 提取排序后的知识点ID
                List<UUID> kpIds = kpEntries.stream()
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());

                // 从每个知识点中选择题目
                int kpSelected = 0;
                for (UUID kpId : kpIds) {
                    List<TrainingQuestionPO> questions = kpQuestionsOfType.get(kpId);
                    // 计算当前知识点应选择的题目数量
                    int toSelect = Math.min(questionsPerKp, questions.size());
                    // 确保不超过总体需要的题目数量
                    toSelect = Math.min(toSelect, kpQuestionCount - kpSelected);

                    // 按审核状态优先级选择题目
                    int selected = selectQuestionsByReviewStatus(
                        questions,
                        toSelect,
                        selectedQuestionIds,
                        selectedQuestions
                    );
                    kpSelected += selected;

                    // 如果已经选够了知识点题目，就退出循环
                    if (kpSelected >= kpQuestionCount) {
                        break;
                    }
                }

                // 如果还没选够，继续从剩余知识点中选择
                if (kpSelected < kpQuestionCount) {
                    List<TrainingQuestionPO> remainingKpQuestions = questionsOfType.stream()
                            .filter(q -> q.getKnowledgePointId() != null && !selectedQuestionIds.contains(q.getQuestionId()))
                            .collect(Collectors.toList());

                    // 从剩余知识点中按审核状态优先级选择题目
                    int remainingNeeded = kpQuestionCount - kpSelected;
                    if (remainingNeeded > 0) {
                        int selected = selectQuestionsByReviewStatus(
                            remainingKpQuestions,
                            remainingNeeded,
                            selectedQuestionIds,
                            selectedQuestions
                        );
                        kpSelected += selected;
                    }
                }
            }

            // 从题型中选择题目
            if (qtCount > 0) {
                // 计算每个题型应选择的题目数量（至少为1）
                int questionsPerQt = Math.max(1, qtQuestionCount / qtCount);

                // 按审核状态对题型进行排序（二审通过 > 一审通过 > 未通过）
                List<Map.Entry<UUID, List<TrainingQuestionPO>>> qtEntries = new ArrayList<>(qtQuestionsOfType.entrySet());
                qtEntries.sort((e1, e2) -> {
                    // 获取每个题型的最高审核状态
                    int status1 = e1.getValue().stream()
                            .mapToInt(q -> q.getReviewStatus() != null ? q.getReviewStatus().getPriority() : 0)
                            .max().orElse(0);
                    int status2 = e2.getValue().stream()
                            .mapToInt(q -> q.getReviewStatus() != null ? q.getReviewStatus().getPriority() : 0)
                            .max().orElse(0);
                    // 降序排序
                    return Integer.compare(status2, status1);
                });

                // 提取排序后的题型ID
                List<UUID> qtIds = qtEntries.stream()
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());

                // 从每个题型中选择题目
                int qtSelected = 0;
                for (UUID qtId : qtIds) {
                    List<TrainingQuestionPO> questions = qtQuestionsOfType.get(qtId);
                    // 计算当前题型应选择的题目数量
                    int toSelect = Math.min(questionsPerQt, questions.size());
                    // 确保不超过总体需要的题目数量，并考虑已经选择的题目
                    toSelect = Math.min(toSelect, qtQuestionCount - qtSelected);

                    // 按审核状态优先级选择题目
                    int selected = selectQuestionsByReviewStatus(
                        questions,
                        toSelect,
                        selectedQuestionIds,
                        selectedQuestions
                    );
                    qtSelected += selected;

                    // 如果已经选够了题型题目，就退出循环
                    if (qtSelected >= qtQuestionCount) {
                        break;
                    }
                }

                // 如果还没选够，继续从剩余题型中选择
                if (qtSelected < qtQuestionCount) {
                    List<TrainingQuestionPO> remainingQtQuestions = questionsOfType.stream()
                            .filter(q -> q.getQuestionTypeId() != null && !selectedQuestionIds.contains(q.getQuestionId()))
                            .collect(Collectors.toList());

                    // 从剩余题型中按审核状态优先级选择题目
                    int remainingNeeded = qtQuestionCount - qtSelected;
                    if (remainingNeeded > 0) {
                        int selected = selectQuestionsByReviewStatus(
                            remainingQtQuestions,
                            remainingNeeded,
                            selectedQuestionIds,
                            selectedQuestions
                        );
                        qtSelected += selected;
                    }
                }
            }
        }

        // 按知识点和题型分类结果
        List<TrainingQuestionPO> kpResults = selectedQuestions.stream()
                .filter(po -> null != po.getKnowledgePointId())
                .collect(Collectors.toList());

        List<TrainingQuestionPO> qtResults = selectedQuestions.stream()
                .filter(po -> null != po.getQuestionTypeId())
                .collect(Collectors.toList());

        // 按题目类型统计选择的题目数量
        Map<QuestionType, Long> selectedTypeCount = selectedQuestions.stream()
                .collect(Collectors.groupingBy(
                        TrainingQuestionPO::getQuestionType,
                        Collectors.counting()
                ));

        // 返回结果
        HashMap<String, Object> result = new HashMap<>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);
        result.put("selectedQuestions", selectedQuestions);
        result.put("totalSelected", selectedQuestionIds.size());
        result.put("typeDistribution", selectedTypeCount);
        return result;
    }



}
