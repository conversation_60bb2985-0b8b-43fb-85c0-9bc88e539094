package com.joinus.knowledge.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.KnowledgePointFileCategory;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathKnowledgePointFiles;
import com.joinus.knowledge.model.param.ImageParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionVO;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathKnowledgePointFilesService;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import com.joinus.knowledge.service.MathQuestionsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 数学知识点管理 Controller
 */
@RestController
@RequestMapping("/api/math/knowledge-points")
@RequiredArgsConstructor
public class MathKnowledgePointsController {

    private final MathKnowledgePointsService mathKnowledgePointsService;
    private final MathQuestionsService mathQuestionsService;
    private final MathKnowledgePointFilesService mathKnowledgePointFilesService;
    private final FilesService filesService;

    /**
     * 查询所有知识点
     * GET /api/math/knowledge-points
     */
    @GetMapping
    public Result<List<MathKnowledgePoint>> list() {
        return Result.success(mathKnowledgePointsService.list());
    }

    /**
     * 分页查询知识点
     * GET /api/math/knowledge-points/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathKnowledgePoint>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        
        Page<MathKnowledgePoint> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(MathKnowledgePoint::getName, name);
        }
        
        // 按排序号排序
        queryWrapper.orderByAsc(MathKnowledgePoint::getSortNo);
        
        Page<MathKnowledgePoint> resultPage = mathKnowledgePointsService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    @PostMapping("/page")
    public Result<Page<MathKnowledgePointVO>> page(@RequestBody PageKnowledgePointParam pageKnowledgePointParam) {
        Page<MathKnowledgePointVO> pageParam = new Page<>(pageKnowledgePointParam.getPage(), pageKnowledgePointParam.getSize());
        Page<MathKnowledgePointVO> resultPage = mathKnowledgePointsService.page(pageParam, pageKnowledgePointParam);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询知识点
     * GET /api/math/knowledge-points/{id}
     */
    @GetMapping("/{id}")
    public Result<MathKnowledgePoint> getById(@PathVariable("id") UUID id) {
        MathKnowledgePoint knowledgePoint = mathKnowledgePointsService.getById(id);
        if (knowledgePoint == null) {
            return Result.error(404, "知识点不存在");
        }
        return Result.success(knowledgePoint);
    }

    @GetMapping("/detail/{id}")
    public Result<MathKnowledgePointVO> getDetailById(@PathVariable("id") UUID id) {
        Page<MathKnowledgePointVO> pageParam = new Page<>(1, 1);
        PageKnowledgePointParam pageKnowledgePointParam = new PageKnowledgePointParam();
        pageKnowledgePointParam.setId(id);
        Page<MathKnowledgePointVO> resultPage = mathKnowledgePointsService.page(pageParam, pageKnowledgePointParam);
        MathKnowledgePointVO knowledgePoint = resultPage.getRecords().getFirst();
        if (knowledgePoint == null) {
            return Result.error(404, "知识点不存在");
        }
        if (StrUtil.isNotBlank(knowledgePoint.getHandout())) {
            knowledgePoint.setHandout(mathQuestionsService.decodeContentV2(knowledgePoint.getHandout()));
        }
        List<MathKnowledgePointFiles> knowledgePointFiles = mathKnowledgePointFilesService.lambdaQuery()
                .eq(MathKnowledgePointFiles::getKnowledgePointId, id)
                .orderByAsc(MathKnowledgePointFiles::getSortNo)
                .list();

        Map<KnowledgePointFileCategory, List<MathKnowledgePointFiles>> map = knowledgePointFiles.stream().collect(Collectors.groupingBy(MathKnowledgePointFiles::getCategory));
        if (map.containsKey(KnowledgePointFileCategory.IMAGE) && CollUtil.isNotEmpty(map.get(KnowledgePointFileCategory.IMAGE))) {
            List<File> files = filesService.listByIds(map.get(KnowledgePointFileCategory.IMAGE).stream().map(MathKnowledgePointFiles::getFileId).collect(Collectors.toList()));
            List<String> images = files.stream().map(file -> filesService.getOssUrl(file.getOssUrl(), OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()))).toList();
            knowledgePoint.setOriginalImageList(images);
        }
        if (map.containsKey(KnowledgePointFileCategory.PPT) && CollUtil.isNotEmpty(map.get(KnowledgePointFileCategory.PPT))) {
            List<File> files = filesService.listByIds(map.get(KnowledgePointFileCategory.PPT).stream().map(MathKnowledgePointFiles::getFileId).collect(Collectors.toList()));
            List<String> ppt = files.stream().map(file -> filesService.getOssUrl(file.getOssUrl(), OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()))).toList();
            knowledgePoint.setPptList(ppt);
        }
        if (map.containsKey(KnowledgePointFileCategory.VIDEO) && CollUtil.isNotEmpty(map.get(KnowledgePointFileCategory.VIDEO))) {
            List<File> files = filesService.listByIds(map.get(KnowledgePointFileCategory.VIDEO).stream().map(MathKnowledgePointFiles::getFileId).collect(Collectors.toList()));
            List<String> video = files.stream().map(file -> filesService.getOssUrl(file.getOssUrl(), OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()))).toList();
            knowledgePoint.setVideoList(video);
        }

        return Result.success(knowledgePoint);
    }

    /**
     * 创建知识点
     * POST /api/math/knowledge-points
     */
    @PostMapping
    public Result<MathKnowledgePoint> create(@RequestBody MathKnowledgePoint knowledgePoint) {
        // 检查是否存在相同的知识点（名称相同）
        LambdaQueryWrapper<MathKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MathKnowledgePoint::getName, knowledgePoint.getName());
        
        // 检查是否已存在
        MathKnowledgePoint existingPoint = mathKnowledgePointsService.getOne(queryWrapper);
        if (existingPoint != null) {
            // 如果已存在相同知识点，返回已存在的知识点
            return Result.error("知识点已存在");
        }
        
        // 生成UUID
        knowledgePoint.generateUUID();
        
        boolean success = mathKnowledgePointsService.save(knowledgePoint);
        if (success) {
            return Result.success(knowledgePoint);
        }
        return Result.error("创建知识点失败");
    }
    
    /**
     * 批量创建知识点
     * POST /api/math/knowledge-points/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathKnowledgePoint> knowledgePointsList) {
        if (knowledgePointsList == null || knowledgePointsList.isEmpty()) {
            return Result.error("知识点列表不能为空");
        }
        
        // 过滤掉重复的知识点
        List<MathKnowledgePoint> pointsToSave = new ArrayList<>();
        
        for (MathKnowledgePoint point : knowledgePointsList) {
            // 检查是否存在相同的知识点（名称相同）
            LambdaQueryWrapper<MathKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MathKnowledgePoint::getName, point.getName());
            
            // 检查是否已存在
            MathKnowledgePoint existingPoint = mathKnowledgePointsService.getOne(queryWrapper);
            if (existingPoint == null) {
                // 只有不存在的知识点才添加到待保存列表
                pointsToSave.add(point);
            }
        }
        
        if (pointsToSave.isEmpty()) {
            // 所有知识点都已存在，无需添加
            return Result.success(true);
        }
        
        // 为每个知识点生成UUID
        pointsToSave.forEach(MathKnowledgePoint::generateUUID);
        
        // 批量保存知识点
        boolean success = mathKnowledgePointsService.saveBatch(pointsToSave);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建知识点失败");
    }
    
    /**
     * 更新知识点
     * PUT /api/math/knowledge-points/{id}
     */
    @PutMapping("/{id}")
    public Result<MathKnowledgePoint> update(@PathVariable("id") UUID id, @RequestBody MathKnowledgePoint knowledgePoint) {
        // 确保要更新的ID正确
        knowledgePoint.setId(id);
        knowledgePoint.setHandout(mathQuestionsService.encodeContent(knowledgePoint.getHandout()));
        boolean success = mathKnowledgePointsService.updateById(knowledgePoint);
        if (success) {
            return Result.success(knowledgePoint);
        }
        return Result.error("更新知识点失败");
    }

    /**
     * 删除知识点
     * DELETE /api/math/knowledge-points/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable("id") UUID id) {
        boolean success = mathKnowledgePointsService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除知识点失败");
    }

    @PostMapping("/list")
    public Result<List<MathKnowledgePointVO>> list(@RequestBody List<UUID> kpIds) {
        return Result.success(mathKnowledgePointsService.listByIds(kpIds));
    }

    @PostMapping("/files/upload/{id}")
    public Result<Boolean> uploadFiles(@PathVariable("id") UUID id, @RequestBody List<UploadFileParam> uploadFiles) {
        mathKnowledgePointFilesService.saveKnowledgePointFileAndRelation(id, uploadFiles);
        return Result.success(true);
    }
}
